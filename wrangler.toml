# Cloudflare Workers 专用配置文件
# CloudNav 导航站 - 纯 Workers 部署架构
# 详细配置说明请参考: docs/DEPLOYMENT.md

name = "cloudnav-navigation"
main = "./dist/_worker.js/index.js"  # Workers 入口文件
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]  # 启用 Node.js 兼容性

# Workers 资源限制配置 - 免费计划不支持 CPU 限制
# [limits]
# cpu_ms = 50  # CPU 时间限制 (毫秒) - 需要付费计划

# KV 存储命名空间绑定 - Workers 统一数据存储
# 所有数据（书签、分类、会话、统计等）都存储在同一个 KV 中
[[kv_namespaces]]
binding = "CLOUDNAV_KV"  # 统一数据存储
id = "8fc61a719ca740fa89dccb3bbe2af652"  # 实际的 KV 命名空间 ID
preview_id = "8fc61a719ca740fa89dccb3bbe2af652"  # 预览环境使用同一个 KV

# Astro 会话管理 KV 绑定（使用同一个 KV 命名空间）
[[kv_namespaces]]
binding = "SESSION"  # Astro 要求的会话存储
id = "8fc61a719ca740fa89dccb3bbe2af652"  # 使用同一个 KV 命名空间
preview_id = "8fc61a719ca740fa89dccb3bbe2af652"  # 预览环境使用同一个 KV

# 环境变量配置
[vars]
# 管理功能开关
ENABLE_ADMIN = "true"
# 数据源模式: "static" 或 "kv"
DATA_SOURCE = "kv"
# 网站基础信息
SITE_NAME = "Cloudnav 导航站"
SITE_DESCRIPTION = "智能化的个人导航站，支持书签管理、AI 分类和数据统计"

# 开发环境配置
[env.development]
[env.development.vars]
ENABLE_ADMIN = "true"
DATA_SOURCE = "static"  # 开发环境使用静态数据
DEBUG = "true"

# 生产环境配置
[env.production]
[env.production.vars]
ENABLE_ADMIN = "false"  # 生产环境默认关闭管理功能
DATA_SOURCE = "kv"
# 生产环境安全配置
PUBLIC_ADMIN_PASSWORD_HASH = ""  # 管理员密码哈希，运行 npm run setup 生成
GEMINI_API_KEY = ""  # Gemini AI API 密钥（可选）

# 预览环境配置
[env.preview]
[env.preview.vars]
ENABLE_ADMIN = "true"
DATA_SOURCE = "kv"

# Workers Assets 静态资源配置
# 通过 Workers 直接提供静态文件，无需 Pages
[assets]
binding = "ASSETS"
directory = "./dist/static"  # 静态资源目录（排除 _worker.js）

# Workers 路由配置 - 使用默认 workers.dev 域名
# 如需自定义域名，请取消注释并配置正确的域名
# [[routes]]
# pattern = "/*"  # 匹配所有路径
# zone_name = "your-domain.com"  # 替换为您的域名

# Workers 可观测性配置
[observability]
enabled = true  # 启用性能监控和日志

# Workers 构建配置 - 已禁用自动构建
# [build]
# command = "npm run build"  # Astro 构建命令
# cwd = "."
# watch_dir = "src"  # 监听源码变化

# 本地开发环境配置
# 注意：miniflare 配置在新版本 wrangler 中已内置，无需显式配置