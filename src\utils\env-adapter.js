/**
 * 环境变量访问适配器
 * 统一处理不同环境下的环境变量访问方式
 * <AUTHOR> 4.0 sonnet
 * @version 1.0.0
 */

/**
 * 获取环境变量值
 * @param {string} key - 环境变量键名
 * @param {Object} context - Astro 上下文对象（在 API 路由中使用）
 * @returns {string|undefined} 环境变量值
 */
export function getEnvVar(key, context = null) {
  // 1. 优先从 Astro 上下文获取（API 路由中）
  if (context?.locals?.runtime?.env) {
    const value = context.locals.runtime.env[key];
    if (value !== undefined) {
      return value;
    }
  }

  // 2. 从 Cloudflare Workers 全局环境获取
  if (typeof globalThis !== 'undefined') {
    // 检查是否在 Workers 环境中
    if (globalThis[key] !== undefined) {
      return globalThis[key];
    }
  }

  // 3. 从 import.meta.env 获取（构建时和客户端）
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    const value = import.meta.env[key];
    if (value !== undefined) {
      return value;
    }
  }

  // 4. 从 process.env 获取（Node.js 环境）
  if (typeof process !== 'undefined' && process.env) {
    const value = process.env[key];
    if (value !== undefined) {
      return value;
    }
  }

  return undefined;
}

/**
 * 检查环境变量是否为真值
 * @param {string} key - 环境变量键名
 * @param {Object} context - Astro 上下文对象
 * @returns {boolean} 是否为真值
 */
export function isEnvTrue(key, context = null) {
  const value = getEnvVar(key, context);
  return value === 'true' || value === '1' || value === 'yes';
}

/**
 * 获取环境变量，如果不存在则返回默认值
 * @param {string} key - 环境变量键名
 * @param {string} defaultValue - 默认值
 * @param {Object} context - Astro 上下文对象
 * @returns {string} 环境变量值或默认值
 */
export function getEnvVarWithDefault(key, defaultValue, context = null) {
  const value = getEnvVar(key, context);
  return value !== undefined ? value : defaultValue;
}

/**
 * 环境变量键名常量
 * 统一管理所有环境变量的键名，避免拼写错误
 */
export const ENV_KEYS = {
  // 管理功能
  ENABLE_ADMIN: 'ENABLE_ADMIN',
  PUBLIC_ENABLE_ADMIN: 'PUBLIC_ENABLE_ADMIN',
  ADMIN_PASSWORD_HASH: 'ADMIN_PASSWORD_HASH',
  PUBLIC_ADMIN_PASSWORD_HASH: 'PUBLIC_ADMIN_PASSWORD_HASH',
  
  // AI 功能
  ENABLE_AI: 'ENABLE_AI',
  GEMINI_API_KEY: 'GEMINI_API_KEY',
  
  // 数据存储
  DATA_SOURCE: 'DATA_SOURCE',
  
  // 站点配置
  SITE_NAME: 'SITE_NAME',
  SITE_DESCRIPTION: 'SITE_DESCRIPTION',
  SITE_URL: 'SITE_URL',
  
  // 调试和环境
  DEBUG: 'DEBUG',
  NODE_ENV: 'NODE_ENV',
  ENVIRONMENT: 'ENVIRONMENT'
};

/**
 * 获取管理功能是否启用
 * @param {Object} context - Astro 上下文对象
 * @returns {boolean} 是否启用管理功能
 */
export function isAdminEnabled(context = null) {
  // 优先检查 PUBLIC_ENABLE_ADMIN（客户端可访问）
  if (isEnvTrue(ENV_KEYS.PUBLIC_ENABLE_ADMIN, context)) {
    return true;
  }
  
  // 然后检查 ENABLE_ADMIN（服务端）
  return isEnvTrue(ENV_KEYS.ENABLE_ADMIN, context);
}

/**
 * 获取管理员密码哈希
 * @param {Object} context - Astro 上下文对象
 * @returns {string|undefined} 密码哈希
 */
export function getAdminPasswordHash(context = null) {
  // 优先检查 PUBLIC_ADMIN_PASSWORD_HASH
  const publicHash = getEnvVar(ENV_KEYS.PUBLIC_ADMIN_PASSWORD_HASH, context);
  if (publicHash) {
    return publicHash;
  }
  
  // 然后检查 ADMIN_PASSWORD_HASH
  return getEnvVar(ENV_KEYS.ADMIN_PASSWORD_HASH, context);
}

/**
 * 获取 Gemini API 密钥
 * @param {Object} context - Astro 上下文对象
 * @returns {string|undefined} API 密钥
 */
export function getGeminiApiKey(context = null) {
  return getEnvVar(ENV_KEYS.GEMINI_API_KEY, context);
}

/**
 * 获取数据源类型
 * @param {Object} context - Astro 上下文对象
 * @returns {string} 数据源类型（'static' 或 'kv'）
 */
export function getDataSource(context = null) {
  return getEnvVarWithDefault(ENV_KEYS.DATA_SOURCE, 'kv', context);
}

/**
 * 检查是否为调试模式
 * @param {Object} context - Astro 上下文对象
 * @returns {boolean} 是否为调试模式
 */
export function isDebugMode(context = null) {
  return isEnvTrue(ENV_KEYS.DEBUG, context);
}

/**
 * 获取当前环境
 * @param {Object} context - Astro 上下文对象
 * @returns {string} 环境名称
 */
export function getEnvironment(context = null) {
  return getEnvVarWithDefault(ENV_KEYS.ENVIRONMENT, 'production', context);
}

/**
 * 打印环境变量调试信息（仅在调试模式下）
 * @param {Object} context - Astro 上下文对象
 */
export function debugEnvVars(context = null) {
  if (!isDebugMode(context)) {
    return;
  }
  
  console.log('🔍 环境变量调试信息:');
  console.log('- 管理功能启用:', isAdminEnabled(context));
  console.log('- 数据源:', getDataSource(context));
  console.log('- 环境:', getEnvironment(context));
  console.log('- 调试模式:', isDebugMode(context));
  console.log('- Gemini API Key 已配置:', !!getGeminiApiKey(context));
  console.log('- 管理员密码哈希已配置:', !!getAdminPasswordHash(context));
}
